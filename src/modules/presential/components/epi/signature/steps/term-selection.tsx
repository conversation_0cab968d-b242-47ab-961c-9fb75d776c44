import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { useFindAllTermsQuery } from "@/modules/presential/hooks/terms/find-all-terms-query.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { cn } from "@/shared/lib/utils";
import { Check, FileText } from "lucide-react";

export const TermSelectionStep = () => {
	const { data: termsData, isLoading } = useFindAllTermsQuery();
	const { signatureData, updateSignatureData } = useEpiSignaturePage();

	const terms = termsData?.success ? termsData.data : [];
	const selectedTermId = signatureData.selectedTermId;

	const handleSelectTerm = (term: ITerm) => {
		updateSignatureData({ selectedTermId: term.id });
	};

	if (isLoading) {
		return (
			<div className="space-y-4">
				<div className="text-center mb-6">
					<h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">Selecione o Termo</h2>
					<p className="text-sm md:text-base text-gray-600">Escolha o termo que será utilizado para a assinatura</p>
				</div>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{Array.from({ length: 6 }).map((_, index) => (
						<Card key={index} className="h-32">
							<CardContent className="p-4">
								<Skeleton className="h-4 w-3/4 mb-2" />
								<Skeleton className="h-3 w-full mb-1" />
								<Skeleton className="h-3 w-2/3" />
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="text-center mb-6">
				<h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">Selecione o Termo</h2>
				<p className="text-sm md:text-base text-gray-600">Escolha o termo que será utilizado para a assinatura de EPI</p>
			</div>

			{terms.length === 0 ? (
				<Card className="text-center py-12">
					<CardContent>
						<FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum termo encontrado</h3>
						<p className="text-gray-600">Não há termos disponíveis para seleção no momento.</p>
					</CardContent>
				</Card>
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{terms.map(term => (
						<Card
							key={term.id}
							className={cn(
								"cursor-pointer transition-all duration-200 hover:shadow-md",
								"border-2",
								selectedTermId === term.id ? "border-primary bg-primary/5 shadow-md" : "border-gray-200 hover:border-gray-300"
							)}
							onClick={() => handleSelectTerm(term)}
						>
							<CardHeader className="pb-3">
								<div className="flex items-start justify-between">
									<FileText
										className={cn("w-6 h-6 flex-shrink-0", selectedTermId === term.id ? "text-primary" : "text-gray-400")}
									/>
									{selectedTermId === term.id && <Check className="w-5 h-5 text-primary" />}
								</div>
							</CardHeader>
							<CardContent className="pt-0">
								<CardTitle className="text-base md:text-lg mb-2 line-clamp-2">{term.fileName}</CardTitle>
								<CardDescription className="text-xs md:text-sm">Termo #{term.id}</CardDescription>
							</CardContent>
						</Card>
					))}
				</div>
			)}

			{selectedTermId && (
				<div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
					<div className="flex items-center gap-2">
						<Check className="w-5 h-5 text-green-600" />
						<span className="text-sm font-medium text-green-800">Termo selecionado com sucesso</span>
					</div>
				</div>
			)}
		</div>
	);
};
