import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { useFindAllTermsQuery } from "@/modules/presential/hooks/terms/find-all-terms-query.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { Card, CardContent } from "@/shared/components/ui/card";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { cn } from "@/shared/lib/utils";
import { format } from "date-fns";
import { pt } from "date-fns/locale";
import { Check, FileText } from "lucide-react";

export const TermSelectionStep = () => {
	const { data: termsData, isLoading } = useFindAllTermsQuery();
	const { signatureData, updateSignatureData } = useEpiSignaturePage();

	const terms = termsData?.success ? termsData.data : [];
	const selectedTermId = signatureData.selectedTermId;
	const selectedTerm = terms.find(term => term.id === selectedTermId);

	const handleSelectTerm = (term: ITerm) => {
		updateSignatureData({ selectedTermId: term.id });
	};

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy", { locale: pt });
		} catch {
			return "Data não disponível";
		}
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				{/* Header compacto */}
				<div className="text-center mb-6">
					<div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-pormade to-green-600 rounded-full mb-3 shadow-md">
						<FileText className="w-6 h-6 text-white" />
					</div>
					<h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">Selecione o Termo</h2>
					<p className="text-sm md:text-base text-gray-600">Escolha o termo que será utilizado para a assinatura de EPI</p>
				</div>

				{/* Loading cards compactos */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{Array.from({ length: 6 }).map((_, index) => (
						<Card key={index} className="h-32 overflow-hidden border-0 shadow-md">
							<div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 p-4">
								<div className="flex items-start justify-between mb-3">
									<Skeleton className="h-6 w-6 rounded-full" />
									<Skeleton className="h-3 w-12 rounded-full" />
								</div>
								<Skeleton className="h-4 w-full mb-2" />
								<Skeleton className="h-3 w-2/3" />
							</div>
						</Card>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header compacto */}
			<div className="text-center mb-6">
				<div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-pormade to-green-600 rounded-full mb-3 shadow-md">
					<FileText className="w-6 h-6 text-white" />
				</div>
				<h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">Selecione o Termo</h2>
				<p className="text-sm md:text-base text-gray-600">Escolha o termo que será utilizado para a assinatura de EPI</p>
			</div>

			{terms.length === 0 ? (
				<Card className="text-center py-12 border-0 shadow-md bg-gradient-to-br from-gray-50 to-gray-100">
					<CardContent className="space-y-3">
						<div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-300 to-gray-400 rounded-full mb-3">
							<FileText className="w-8 h-8 text-white" />
						</div>
						<h3 className="text-lg font-semibold text-gray-900">Nenhum termo encontrado</h3>
						<p className="text-gray-600 text-sm max-w-md mx-auto">Não há termos disponíveis para seleção no momento.</p>
					</CardContent>
				</Card>
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{terms.map(term => (
						<Card
							key={term.id}
							className={cn(
								"group cursor-pointer transition-all duration-200 hover:scale-[1.01] hover:shadow-lg",
								"border-0 shadow-md overflow-hidden h-36",
								selectedTermId === term.id ? "ring-2 ring-pormade shadow-pormade/20" : "hover:shadow-gray-200"
							)}
							onClick={() => handleSelectTerm(term)}
						>
							<div
								className={cn(
									"h-full bg-gradient-to-br transition-all duration-200 p-4",
									selectedTermId === term.id
										? "from-pormade/5 to-green-50"
										: "from-white to-gray-50 group-hover:from-gray-50 group-hover:to-gray-100"
								)}
							>
								<div className="flex items-start justify-between mb-3">
									<div
										className={cn(
											"p-2 rounded-full transition-all duration-200",
											selectedTermId === term.id
												? "bg-pormade text-white shadow-md"
												: "bg-gray-100 text-gray-400 group-hover:bg-gray-200"
										)}
									>
										<FileText className="w-4 h-4" />
									</div>
									{selectedTermId === term.id && (
										<div className="flex items-center gap-1 bg-pormade text-white px-2 py-1 rounded-full text-xs font-medium shadow-md">
											<Check className="w-3 h-3" />
											Selecionado
										</div>
									)}
								</div>
								<div className="space-y-2">
									<h3 className="text-base font-semibold line-clamp-2 text-gray-900">{term.title || term.fileName}</h3>
									<div className="flex items-center gap-2 text-xs text-gray-600">
										<div className="w-1.5 h-1.5 bg-pormade rounded-full"></div>
										<span>Termo #{term.id}</span>
									</div>
								</div>
							</div>
						</Card>
					))}
				</div>
			)}

			{/* Feedback de seleção melhorado */}
			{selectedTermId && selectedTerm && (
				<Card className="border-0 shadow-lg bg-gradient-to-r from-green-50 to-emerald-50">
					<CardContent className="p-6">
						<div className="flex items-start gap-4">
							<div className="flex-shrink-0">
								<div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
									<Check className="w-6 h-6 text-white" />
								</div>
							</div>
							<div className="flex-1">
								<h3 className="text-lg font-semibold text-green-900 mb-2">Termo selecionado com sucesso!</h3>
								<p className="text-green-800 mb-3">
									<strong>{selectedTerm.title || selectedTerm.fileName}</strong>
								</p>
								<p className="text-sm text-green-700">
									Você pode prosseguir para a próxima etapa ou alterar sua seleção a qualquer momento.
								</p>
							</div>
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
};
