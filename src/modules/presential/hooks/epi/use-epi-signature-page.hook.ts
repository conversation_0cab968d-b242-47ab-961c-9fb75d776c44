import { useCallback, useState } from "react";

export interface IEpiSignatureData {
	selectedPersonId?: number;
	selectedTermId?: number;
	selectedEpiGroupId?: number;
	selectedEpis?: any[];
	epiItems?: any[];
	personCpf?: string;
	personName?: string;
	photo?: File;
	signatoryCount?: number;
	signature?: string;
}

export interface IHandleEpiSignaturePageHook {
	currentStep: number;
	isLoading: boolean;
	canProceed: boolean;
	signatureData: IEpiSignatureData;
	handleNextStep: () => void;
	handlePreviousStep: () => void;
	handleStepChange: (step: number) => void;
	updateSignatureData: (data: Partial<IEpiSignatureData>) => void;
	resetProcess: () => void;
	validateCurrentStep: () => boolean;
}

export const useEpiSignaturePage = (): IHandleEpiSignaturePageHook => {
	const [currentStep, setCurrentStep] = useState(1);
	const [isLoading, setIsLoading] = useState(false);
	const [signatureData, setSignatureData] = useState<IEpiSignatureData>({});

	const validateCurrentStep = useCallback((): boolean => {
		switch (currentStep) {
			case 1:
				// Validar se termo foi selecionado
				return !!signatureData.selectedTermId;
			case 2:
				// Validar se EPIs foram selecionados e quantidade definida
				return !!(signatureData.selectedEpis?.length && signatureData.signatoryCount);
			case 3:
				// Validar se CPF, nome e foto foram preenchidos
				return !!(signatureData.personCpf && signatureData.personName && signatureData.photo);
			case 4:
				// Validar se assinatura foi realizada
				return !!signatureData.signature;
			case 5:
				// Etapa de confirmação - sempre válida se chegou até aqui
				return true;
			default:
				return false;
		}
	}, [currentStep, signatureData]);

	const canProceed = validateCurrentStep();

	const handleNextStep = useCallback(() => {
		if (canProceed && currentStep < 5) {
			setCurrentStep(prev => prev + 1);
		}
	}, [canProceed, currentStep]);

	const handlePreviousStep = useCallback(() => {
		if (currentStep > 1) {
			setCurrentStep(prev => prev - 1);
		}
	}, [currentStep]);

	const handleStepChange = useCallback((step: number) => {
		if (step >= 1 && step <= 5) {
			setCurrentStep(step);
		}
	}, []);

	const updateSignatureData = useCallback((data: Partial<IEpiSignatureData>) => {
		setSignatureData(prev => ({ ...prev, ...data }));
	}, []);

	const resetProcess = useCallback(() => {
		setCurrentStep(1);
		setSignatureData({});
		setIsLoading(false);
	}, []);

	return {
		currentStep,
		isLoading,
		canProceed,
		signatureData,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		updateSignatureData,
		resetProcess,
		validateCurrentStep,
	};
};
